import { TSESLint } from '@typescript-eslint/utils';
export type MessageIds = 'unusedVar' | 'usedIgnoredVar' | 'usedOnlyAsType';
export type Options = [
    'all' | 'local' | {
        args?: 'after-used' | 'all' | 'none';
        argsIgnorePattern?: string;
        caughtErrors?: 'all' | 'none';
        caughtErrorsIgnorePattern?: string;
        destructuredArrayIgnorePattern?: string;
        ignoreClassWithStaticInitBlock?: boolean;
        ignoreRestSiblings?: boolean;
        reportUsedIgnorePattern?: boolean;
        vars?: 'all' | 'local';
        varsIgnorePattern?: string;
    }
];
declare const _default: TSESLint.RuleModule<MessageIds, Options, import("../../rules").ESLintPluginDocs, TSESLint.RuleListener>;
export default _default;
//# sourceMappingURL=no-unused-vars.d.ts.map