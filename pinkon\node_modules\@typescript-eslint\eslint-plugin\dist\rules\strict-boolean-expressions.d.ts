export type Options = [
    {
        allowAny?: boolean;
        allowNullableBoolean?: boolean;
        allowNullableEnum?: boolean;
        allowNullableNumber?: boolean;
        allowNullableObject?: boolean;
        allowNullableString?: boolean;
        allowNumber?: boolean;
        allowRuleToRunWithoutStrictNullChecksIKnowWhatIAmDoing?: boolean;
        allowString?: boolean;
    }
];
type ConditionErrorMessageId = 'conditionErrorAny' | 'conditionErrorNullableBoolean' | 'conditionErrorNullableEnum' | 'conditionErrorNullableNumber' | 'conditionErrorNullableObject' | 'conditionErrorNullableString' | 'conditionErrorNullish' | 'conditionErrorNumber' | 'conditionErrorObject' | 'conditionErrorOther' | 'conditionErrorString';
export type MessageId = 'conditionFixCastBoolean' | 'conditionFixCompareArrayLengthNonzero' | 'conditionFixCompareArrayLengthZero' | 'conditionFixCompareEmptyString' | 'conditionFixCompareFalse' | 'conditionFixCompareNaN' | 'conditionFixCompareNullish' | 'conditionFixCompareStringLength' | 'conditionFixCompareTrue' | 'conditionFixCompareZero' | 'conditionFixDefaultEmptyString' | 'conditionFixDefaultFalse' | 'conditionFixDefaultZero' | 'explicitBooleanReturnType' | 'noStrictNullCheck' | 'predicateCannotBeAsync' | ConditionErrorMessageId;
declare const _default: import("@typescript-eslint/utils/ts-eslint").RuleModule<MessageId, Options, import("../../rules").ESLintPluginDocs, import("@typescript-eslint/utils/ts-eslint").RuleListener>;
export default _default;
//# sourceMappingURL=strict-boolean-expressions.d.ts.map