export type Options = [
    {
        checkLiteralConstAssertions?: boolean;
        typesToIgnore?: string[];
    }
];
export type MessageIds = 'contextuallyUnnecessary' | 'unnecessaryAssertion';
declare const _default: import("@typescript-eslint/utils/ts-eslint").RuleModule<MessageIds, Options, import("../../rules").ESLintPluginDocs, import("@typescript-eslint/utils/ts-eslint").RuleListener>;
export default _default;
//# sourceMappingURL=no-unnecessary-type-assertion.d.ts.map