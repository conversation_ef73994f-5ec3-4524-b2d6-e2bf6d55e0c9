export type Options = [
    {
        allowDestructuring?: boolean;
        allowedNames?: string[];
    }
];
export type MessageIds = 'thisAssignment' | 'thisDestructure';
declare const _default: import("@typescript-eslint/utils/ts-eslint").RuleModule<MessageIds, Options, import("../../rules").ESLintPluginDocs, import("@typescript-eslint/utils/ts-eslint").RuleListener>;
export default _default;
//# sourceMappingURL=no-this-alias.d.ts.map